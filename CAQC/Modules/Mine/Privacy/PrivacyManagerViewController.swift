//
//  PrivacyManagerViewController.swift
//  CAQC
//
//  Created by mn on 2025/5/8.
//
import DeepalShield
import SLBaseUI
import UIComponent

class PrivacyManagerViewController: ComponentViewController {
  var navigationBar: CustomNavigationBar = .init(frame: .zero)

  lazy var datasource: [SettingItem] = [SettingItem(title: "黑名单管理", tapHandler: { _ in
    let vc = ShieldViewController()
    Router.push(vc, animated: true)
  })]

  override func buildComponent() -> any Component {
    return VStack {
      for item in datasource {
        SettingItemCard(item: item)
          .size(height: 56)
          .id(item.title)
      }
    }
  }

  override func makeUI() {
    navigationBar.setTitle("隐私管理", mode: .large)
    view.addSubview(navigationBar)
    navigationBar.snp.makeConstraints { make in
      make.top.equalToSuperview()
      make.leading.trailing.equalToSuperview()
    }
    navigationBar.observeScrollView(componentView)
    componentView.alwaysBounceVertical = true
  }
}
