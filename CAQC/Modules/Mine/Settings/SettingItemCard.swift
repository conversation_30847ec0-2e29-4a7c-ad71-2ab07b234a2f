//
//  SettingItemCard.swift
//  CAQC
//
//  Created by FD on 3/24/25.
//

import Foundation
import SLBaseUI
import UIComponent

struct SettingItemCard: ComponentBuilder {
  let item: SettingItem

  func build() -> some Component {
    return HStack(spacing: 8, justifyContent: .spaceBetween, alignItems: .center) {
      Text(item.title, font: .pf_regular(16))
        .textColor(.Text.primary)

      HStack(spacing: 12, justifyContent: .end, alignItems: .center) {
        if !item.tips.isNilOrEmpty {
          Text(item.tips!, font: .pf_regular(14))
            .textColor(.Text.primary)
            .inset(v: 4, h: 12)
            .size(height: 28)
            .layerBorderWidth(1 / UIScreen.main.scale)
            .layerBorderColor(.Border.secondary)
            .roundedCorner()
        }
        HStack(spacing: 0, alignItems: .center) {
          if !item.detail.isNilOrEmpty {
            Text(item.detail!, font: .pf_regular(16))
              .textColor(.Text.secondary)
              .size(width: .absolute(item.detail!.getTextWidth(font: .pf_regular(16), heigh: 23).ceil))
          }

          Image(.icon(.iconArrowRight, fontSize: 18, color: .Icon.secondary))
        }
      }
    }.tappableView {
      if item.needLogin {
        CALoginManager.ca_isLogin { flag in
          if flag {
            item.tapHandler?(item)
          }
        }
      } else {
        item.tapHandler?(item)
      }
    }
    .inset(h: 20)
  }
}
