//
//  SettingViewController.swift
//  CAQC
//
//  Created by FD on 3/19/25.
//

import CAAutonym
import DeepalShield
import ReactorKit
import RxCocoa
import RxSwift
import SLBaseUI
import SLEnvConfig
import SLFoundation
import SLServiceProtocol
import SwiftyJSON
import UIComponent

class SettingViewController: ComponentViewController, View {
  // MARK: Internal

  // MARK: - Types

  typealias Reactor = SettingReactor

  var navigationBar: CustomNavigationBar = .init(frame: .zero)
  lazy var faceController = CAAutonymWebViewController(type: .deepal)

  // MARK: - Initialization
  override func buildComponent() -> any Component {
    guard let reactor = reactor else {
      return VStack {}
    }

    let items = reactor.currentState.items

    return VStack(justifyContent: .spaceBetween) {
      VStack {
        for item in items {
          SettingItemCard(item: item)
            .size(height: 56)
            .id(item.title)
        }
      }

      if CALoginUserInfoObj.sharedInstance().isLogin {
        Button("退出登录") { [weak self] in
          self?.showLogoutConfirmation()
        }
        .font(.pf_medium(16))
        .titleColorForNormal(.Text.primary)
        .layerBorderColor(.Border.primary)
        .layerBorderWidth(1)
        .size(width: .fill, height: 40)
        .roundedCorner()
        .inset(left: 20, bottom: 16 + ca_safetInsets().bottom, right: 20)
      }
    }
  }

  
  init() {
    super.init(nibName: nil, bundle: nil)
    reactor = SettingReactor()

  }
  
  @MainActor required init?(coder: NSCoder) {
    fatalError("init(coder:) has not been implemented")
  }
  
  override func makeUI() {
    navigationBar.setTitle("设置", mode: .large)
    view.addSubview(navigationBar)

    navigationBar.snp.makeConstraints { make in
      make.top.equalToSuperview()
      make.leading.trailing.equalToSuperview()
    }

    navigationBar.observeScrollView(componentView)
    componentView.alwaysBounceVertical = true
  }

  // MARK: - ReactorKit Binding

  func bind(reactor: SettingReactor) {
    // Action
    rx.viewDidLoad
      .map { Reactor.Action.viewDidLoad }
      .bind(to: reactor.action)
      .disposed(by: disposeBag)

    Observable
      .merge(
        NotificationCenter.default.rx.notification(.didLogout),
        NotificationCenter.default.rx.notification(.didLogin)
      )
      .map { _ in Reactor.Action.refreshData }
      .bind(to: reactor.action)
      .disposed(by: disposeBag)

    // State
    reactor.state.map { $0.items }
      .distinctUntilChanged()
      .subscribe(onNext: { [weak self] _ in
        self?.reloadComponent()
      })
      .disposed(by: disposeBag)

    reactor.state.map { $0.isLoading }
      .distinctUntilChanged()
      .subscribe(onNext: { [weak self] isLoading in
        if isLoading {
          self?.showHUD()
        } else {
          self?.hideHUD()
        }
      })
      .disposed(by: disposeBag)

    reactor.state.compactMap { $0.error }
      .subscribe(onNext: { [weak self] error in
        self?.makeToast(error.localizedDescription)
      })
      .disposed(by: disposeBag)
  }

  // MARK: Private

  // MARK: - Navigation Methods

  private func handleItemTap(_ itemType: SettingItemType?) {
    guard let itemType
    else {
      return
    }
    switch itemType {
    case .personalInfo:
      RouterOC.personalInfo()

    case .carVerification:
      carVerification()

    case .notificationSettings:
      let vc = CAMessageSwitchesVC.viewController(
        withViewModelName: "CAMessageSwitchesViewModel",
        parameter: nil
      )
      Router.push(vc, animated: true)

    case .permissionSettings:
      let vc = PermissionViewController()
      Router.push(vc, animated: true)

    case .privacyManagement:
      let vc = PrivacyManagerViewController()
      Router.push(vc, animated: true)

    case .clearCache:
      clearCache()

    case .accountSecurity:
      let vc = SLAccountSecureController()
      Router.push(vc, animated: true)

    case .aboutDeepal:
      let vc = AboutDeepalViewController()
      Router.push(vc, animated: true)

    case .versionUpdate:
      updateVersion()
    }
  }

  private func showLogoutConfirmation() {
    if PostToOCTool.uploading() {
      makeToast("你有正在上传的视频，请稍后再试")
      return
    }

    let alert = CustomAlertController(title: "退出登录", content: "确定退出当前账号？")
    alert.addAction(.init(title: "取消", style: .secondary, handler: nil))
    alert.addAction(.init(title: "确定", style: .primary, handler: { [weak self] in
      self?.reactor?.action.onNext(.logout)
    }))
    present(alert, animated: true)
  }

  // MARK: - Private Methods

  private func carVerification() {
    faceController.getAppDataback = { [weak self] id, info in
      guard let self,
            let id
      else {
        return
      }

      switch info {
      case "token":
        CALoginUserInfoObj.cacTokenBlock { token in
          self.faceController.setAppData(id: id, data: token)
        }

      case "phone":
        faceController.setAppData(id: id, data: CALoginUserInfoObj.userInfo()?.mobile ?? "")

      case "baseUrl":
        faceController.setAppData(id: id, data: SLHttpAddress.baseWebURL)

      default:
        break
      }
    }

    faceController.tokenFailureBlack = { [weak self] id, _ in
      guard let self,
            let id
      else {
        return
      }
      CALoginUserInfoObj.cacTokenBlock { token in
        self.faceController.refaceToken(id: id, data: token)
      }
    }

    faceController.navBack = { [weak self] in
      self?.dismiss(animated: true)
    }

    faceController.modalPresentationStyle = .fullScreen
    present(faceController, animated: true)
  }

  private func updateVersion() {
    guard let reactor = reactor,
          let item = reactor.currentState.items.first(where: { $0.type == .versionUpdate })
    else {
      return
    }

    if !item.url.isNilOrEmpty {
      Router.route(to: item.url!)
      return
    }

    showHUD()
    CAAppManager.sharedManager().getGrayAppVersion { flag, msg in
      self.hideHUD()
      if flag {
        if CAAppManager.sharedManager().grayVersionModel?.reLogin == "Y" {
          self.makeToast(CAAppManager.sharedManager().grayVersionModel?.depiction ?? "")
          ca_dispatch_after(2) {
            self.goDirect(toLoginPage: { _ in })
          }

        } else if CAAppManager.sharedManager().updateType == .none {
          self.makeToast("已是最新版本")
        } else if CAAppManager.sharedManager().updateType != .none {
          CAAppManager.showGrayUpdateAlert(with: CAAppManager.sharedManager().grayVersionModel, isOperation: true)
        }

      } else {
        self.makeToast(msg ?? "已是最新版本")
      }
    }
  }

  private func clearCache() {
    let alert = CustomAlertController(message: "确定清除缓存？")
    alert.addAction(.init(title: "取消", style: .secondary, handler: nil))
    alert.addAction(.init(title: "确定", style: .primary, handler: {
      [weak self] in
      guard let self
      else {
        return
      }
      MBProgressHUD.showHud(text: "清除缓存中...")
      DispatchQueue.main.asyncAfter(delay: 1) {
        CAClearCacheTool.clearWebCache()
        CAClearCacheTool.clearCache { _, flag in
          MBProgressHUD.hideWindowPAG()
          if flag {
            MBProgressHUD.showHud(text: "清除成功")
            DispatchQueue.main.asyncAfter(delay: 1) {
              MBProgressHUD.hideWindowPAG()
              // Trigger refresh to update cache size
              self.reactor?.action.onNext(.refreshData)
            }
          } else {
            self.makeToast("操作失败，请稍后重试")
          }
        }
      }
    }))

    present(alert, animated: true)
  }
}
